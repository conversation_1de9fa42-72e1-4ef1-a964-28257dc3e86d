<?php

namespace app\adminapi\lists\audit;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\user\UserDataReview;


/**
 * UserDataReview列表
 * Class AuditLists
 * @package app\adminapi\lists
 */
class AuditLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function setSearch(): array
    {
        return [
            '=' => [ 'type', 'status'],
        ];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function lists(): array
    {
        $params = $this->params;
        $query = UserDataReview::alias('udr')
            ->join('user u', 'udr.user_id = u.id')
            ->where('udr.type', $params['type'])
            ->whereNull('u.delete_time')
            ->field('udr.id, udr.user_id, udr.image, udr.thumb_image, udr.type, udr.status, udr.reason, udr.create_time,udr.update_time, udr.admin_id, udr.admin_account, u.nickname, u.mobile');

        // 添加查询条件
        if (isset($this->params['status']) && $this->params['status'] !== '' && $this->params['status'] !== null) {
            $query->where('udr.status', $params['status']);
        }
        if (!empty($params['keyword'])) {
            $query->Where(function($q) use ($params) {
                $q->whereNull('delete_time')
                ->where(function($q2) use ($params) {
                    $q2->whereOr('u.id', 'like', '%'.$params['keyword'].'%')
                        ->whereOr('u.nickname', 'like', '%'.$params['keyword'].'%')
                        ->whereOr('u.mobile', 'like', '%'.$params['keyword'].'%');
                });
            });
        }
        return $query->limit($this->limitOffset, $this->limitLength)
            ->order('udr.id', 'desc')
            ->append(['status_desc'])
            ->select()
            ->toArray();

    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function count(): int
    {
        return UserDataReview::where($this->searchWhere)->count();
    }

}