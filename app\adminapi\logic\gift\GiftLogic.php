<?php

namespace app\adminapi\logic\gift;


use app\common\model\gift\Gift;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * Gift逻辑
 * Class GiftLogic
 * @package app\adminapi\logic
 */
class GiftLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            Gift::create([
                'name' => $params['name'],
                'coin' => $params['coin'],
                'img' => $params['img'],
                'svga' => $params['svga'],
                'addtime' => time(),
                'orderno' => $params['orderno'],
                'type' => $params['type'],
                'gift_type' => $params['gift_type'],
                'is_all_notify' => $params['is_all_notify'],
                'is_accost' => $params['is_accost'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            Gift::where('id', $params['id'])->update([
                'name' => $params['name'],
                'coin' => $params['coin'],
                'img' => $params['img'],
                'svga' => $params['svga'],
                'orderno' => $params['orderno'],
                'type' => $params['type'],
                'gift_type' => $params['gift_type'],
                'is_all_notify' => $params['is_all_notify'],
                'is_accost' => $params['is_accost'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
    

    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public static function detail($params): array
    {
        return Gift::findOrEmpty($params['id'])->toArray();
    }
}