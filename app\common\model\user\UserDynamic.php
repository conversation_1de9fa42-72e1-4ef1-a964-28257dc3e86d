<?php

namespace app\common\model\user;

use app\common\model\BaseModel;
use think\model\concern\SoftDelete;
use app\common\service\FileService;
use app\common\service\TimeService;

/**
 * 用户动态模型
 * Class UserDynamic
 * @package app\common\model\user
 */
class UserDynamic extends BaseModel
{
    use SoftDelete;

    protected $name = 'user_dynamic';
    protected $deleteTime = 'delete_time';


    /**
     * @notes 关联用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @notes 关联动态点赞模型
     * @return \think\model\relation\HasMany
     */
    public function likes()
    {
        return $this->hasMany(UserDynamicLike::class, 'dynamic_id', 'id');
    }

    /**
     * @notes 关联动态评论模型
     * @return \think\model\relation\HasMany
     */
    public function comments()
    {
        return $this->hasMany(UserDynamicComment::class, 'dynamic_id', 'id');
    }

    /**
     * @notes 获取媒体文件数组
     * @param $value
     * @return array
     */
    public function getMediaUrlsAttr($value)
    {
        $mediaUrls = json_decode($value, true);
        if (is_array($mediaUrls)) {
            foreach ($mediaUrls as &$url) {
                $url = FileService::getFileUrl($url);
            }
        }
        return $mediaUrls;
    }

    /**
     * @notes 设置媒体文件数组
     * @param $value
     * @return string
     */
    public function setMediaUrlsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }

    /**
     * @notes 搜索器-用户ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchUserIdAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * @notes 搜索器-动态类型
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchTypeAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('type', $value);
        }
    }

    /**
     * @notes 搜索器-状态
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchStatusAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('status', $value);
        }
    }

    /**
     * @notes 搜索器-隐私类型
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchPrivacyTypeAttr($query, $value, $data)
    {
        if ($value !== '') {
            $query->where('privacy_type', $value);
        }
    }

    /**
     * @notes 用户信息获取器
     * @param $value
     * @param $data
     * @return array
     */
    public function getUserInfoAttr($value, $data)
    {
        if (!isset($data['user']) || !$data['user']) {
            return [];
        }

        $user = $data['user'];
        return [
            'user_id'   => $user['id'] ?? 0,
            'nickname'  => $user['nickname'] ?? '',
            'avatar'    => $user['avatar'] ?? '',
            'sex'       => $user['sex'] ?? 0,
            'is_online' => $user['is_online'] ?? 0,
            'is_busy'   => $user['is_busy'] ?? 0,
        ];
    }

    /**
     * @notes 相对时间获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getRelativeTimeAttr($value, $data)
    {
        $createTime = $data['create_time'] ?? time();
        return TimeService::getRelativeTime(is_numeric($createTime) ? $createTime : strtotime($createTime));
    }

    /**
     * @notes 当前用户是否点赞获取器
     * @param $value
     * @param $data
     * @return bool
     */
    public function getIsLikedAttr($value, $data)
    {
        // 从全局获取当前用户ID，或者从模型属性中获取
        $currentUserId = $this->getCurrentUserId();

        if (!$currentUserId) {
            return false;
        }

        return UserDynamicLike::where([
            'dynamic_id' => $data['id'] ?? $this->id,
            'user_id' => $currentUserId
        ])->count() > 0;
    }

    /**
     * @notes 设置当前用户ID（用于点赞状态判断）
     * @param int $userId
     * @return $this
     */
    public function setCurrentUserId(int $userId)
    {
        $this->current_user_id = $userId;
        return $this;
    }

    /**
     * @notes 获取当前用户ID
     * @return int
     */
    protected function getCurrentUserId(): int
    {
        // 优先从模型属性中获取
        if (isset($this->current_user_id)) {
            return $this->current_user_id;
        }

        // 可以从其他地方获取，比如session、request等
        // 这里暂时返回0，实际使用时需要根据具体情况获取
        return 0;
    }

    /**
     * @notes 状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? $this->status;
        switch ($status) {
            case 0:
                return '审核中';
            case 1:
                return '已发布';
            case 2:
                return '审核未通过';
            default:
                return '未知状态';
        }
    }

    /**
     * @notes 隐私类型文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getPrivacyTypeTextAttr($value, $data)
    {
        $privacyType = $data['privacy_type'] ?? $this->privacy_type;
        switch ($privacyType) {
            case 0:
                return '公开';
            case 1:
                return '好友可见';
            case 2:
                return '私密';
            default:
                return '公开';
        }
    }

    /**
     * @notes 类型描述获取器
     * @param $value
     * @return string
     */
    public function getTypeTextAttr($value, $data)
    {
        $typeMap = [
            1 => '图片',
            2 => '视频'
        ];
        return $typeMap[$data['type']] ?? '未知类型';
    }
}
