<?php

namespace app\common\model\user;

use app\common\model\BaseModel;
use app\common\service\FileService;

/**
 * 用户资料审核模型
 * Class UserDataReview
 * @package app\common\model\user
 */
class UserDataReview extends BaseModel
{

    /**
     * @notes 创建时间获取器
     * @param $value
     * @return string
     */
    public function getCreateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * @notes 更新时间获取器
     * @param $value
     * @return string
     */
    public function getUpdateTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : '';
    }

    /**
     * @notes 审核状态描述获取器
     * @param $value
     * @return string
     */
    public function getStatusDescAttr($value, $data)
    {
        $statusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核驳回'
        ];
        return $statusMap[$data['status']] ?? '未知状态';
    }

    /**
     * @notes 类型描述获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getTypeDescAttr($value, $data)
    {
        $typeMap = [
            1 => '头像',
            2 => '照片墙',
            3 => '主播认证'
        ];
        return $typeMap[$data['type']] ?? '未知类型';
    }

    /**
     * 定义与用户表的关联关系
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


}
