<?php

namespace app\adminapi\logic\user;


use app\common\model\level\Level;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * Level逻辑
 * Class LevelLogic
 * @package app\adminapi\logic
 */
class LevelLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            Level::create([
                'levelid' => $params['levelid'],
                'level_up' => $params['level_up'],
                'level_up_female' => $params['level_up_female'],
                'level_bg' => $params['level_bg'],
                'anchor_bg' => $params['anchor_bg'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            Level::where('levelid', $params['levelid'])->update([
                'level_up' => $params['level_up'],
                'level_up_female' => $params['level_up_female'],
                'level_bg' => $params['level_bg'],
                'anchor_bg' => $params['anchor_bg'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }



    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public static function detail($params): array
    {
        return Level::where('levelid',$params['levelid'])->findOrEmpty()->toArray();
    }
}