<?php

namespace app\common\model\banner;

use app\common\model\BaseModel;
use app\common\service\FileService;
class Banner extends BaseModel
{
    
    protected $type = [
        'status' => 'integer',
        'position' => 'integer',
        'sort' => 'integer'
    ];

    /**
     * 设置图片域名
     * @param $value
     * @param $data
     * @return string
     */
    public function getImageAttr($value)
    {
         return trim($value) ? FileService::getFileUrl($value) : '';
    }

    public function searchTitleAttr($query, $value)
    {
        return $value ? $query->where('title', 'like', "%" .$value ."%") : $query;
    }

    public function searchStatusAttr($query, $value)
    {
        return $value !== null ? $query->where('status', $value) : $query;
    }

    /**
     * @notes 轮播图位置获取器
     * @param $value
     * @return string
     */
    public function getPositionDescAttr($value, $data)
    {
        $positionMap = [
            1 => '首页'
        ];
        return $positionMap[$data['position']] ?? '未知位置';
    }

}
