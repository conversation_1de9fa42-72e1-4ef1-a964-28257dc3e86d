<?php

namespace app\adminapi\validate\audit;


use app\common\validate\BaseValidate;


/**
 * UserDataReview验证器
 * Class AuditValidate
 * @package app\adminapi\validate
 */
class AuditValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',
    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
    ];


    /**
     * @notes 编辑场景
     * @return AuditValidate
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function sceneEdit()
    {
        return $this->only(['id']);
    }

}