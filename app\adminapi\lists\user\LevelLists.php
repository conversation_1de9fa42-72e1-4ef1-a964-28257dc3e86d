<?php

namespace app\adminapi\lists\user;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\level\Level;
use app\common\lists\ListsSearchInterface;


/**
 * Level列表
 * Class LevelLists
 * @package app\adminapi\lists
 */
class LevelLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function setSearch(): array
    {
        return [];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function lists(): array
    {
        return Level::where($this->searchWhere)
            ->field(['levelid', 'level_up', 'level_up_female', 'level_bg', 'anchor_bg','create_time'])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['levelid' => 'desc'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function count(): int
    {
        return Level::where($this->searchWhere)->count();
    }

}