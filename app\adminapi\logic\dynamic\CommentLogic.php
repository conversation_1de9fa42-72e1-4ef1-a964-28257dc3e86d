<?php

namespace app\adminapi\logic\dynamic;


use app\common\model\user\UserDynamicComment;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * UserDynamicComment逻辑
 * Class UserDynamicCommentLogic
 * @package app\adminapi\logic
 */
class CommentLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            UserDynamicComment::create([
                'dynamic_id' => $params['dynamic_id'],
                'user_id' => $params['user_id'],
                'parent_id' => $params['parent_id'],
                'reply_to_user_id' => $params['reply_to_user_id'],
                'content' => $params['content'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public static function audit(array $params): bool
    {
        Db::startTrans();
        try {
            $userDynamicComment = UserDynamicComment::find($params['id']);
            $userDynamicComment->status = $params['status'];
            $userDynamicComment->save();
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public static function delete(array $params): bool
    {
        return UserDynamicComment::destroy($params['id']);
    }


    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public static function detail($params): array
    {
        return UserDynamicComment::alias('udc')
            ->join('user u', 'udc.user_id = u.id')
            ->join('user_dynamic ud', 'udc.dynamic_id = ud.id')
            ->field([
                'udc.id', 'udc.content', 'udc.status', 'udc.create_time',
                'u.id as user_id', 'u.nickname', 'u.mobile',
                'ud.id as dynamic_id', 'ud.content as dynamic_content'
            ])->findOrEmpty($params['id'])
            ->toArray();
    }
}