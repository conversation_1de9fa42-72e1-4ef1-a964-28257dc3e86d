<?php


namespace app\adminapi\controller\dynamic;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\dynamic\CommentLists;
use app\adminapi\logic\dynamic\CommentLogic;
use app\adminapi\validate\dynamic\CommentValidate;


/**
 * UserDynamicComment控制器
 * Class UserDynamicCommentController
 * @package app\adminapi\controller
 */
class CommentController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function lists()
    {
        return $this->dataLists(new CommentLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function add()
    {
        $params = (new CommentValidate())->post()->goCheck('add');
        $result = CommentLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(CommentLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function audit()
    {
        $params = (new CommentValidate())->post()->goCheck('edit');
        $result = CommentLogic::audit($params);
        if (true === $result) {
            return $this->success('修改成功', [], 1, 1);
        }
        return $this->fail(CommentLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function delete()
    {
        $params = (new CommentValidate())->post()->goCheck('delete');
        CommentLogic::delete($params);
        return $this->success('删除成功', [], 1, 1);
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function detail()
    {
        $params = (new CommentValidate())->goCheck('detail');
        $result = CommentLogic::detail($params);
        return $this->data($result);
    }


}