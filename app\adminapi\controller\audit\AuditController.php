<?php


namespace app\adminapi\controller\audit;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\audit\AuditLists;
use app\adminapi\logic\audit\AuditLogic;
use app\adminapi\validate\audit\AuditValidate;


/**
 * UserDataReview控制器
 * Class AuditController
 * @package app\adminapi\controller
 */
class AuditController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function lists()
    {
        return $this->dataLists(new AuditLists());
    }

    /**
     * @notes 审核
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public function audit()
    {
        $params = (new AuditValidate())->post()->goCheck('edit');
        $result = AuditLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(AuditLogic::getError());
    }



}