<?php

namespace app\applent\lists\user;

use app\applent\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\user\UserDynamicComment;
use app\common\model\user\User;

/**
 * 动态评论列表
 * Class CommentLists
 * @package app\applent\lists\user
 */
class DynamicCommentLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
        return [];
    }

    /**
     * @notes 获取动态评论列表
     * @return array
     */
    public function lists(): array
    {
        // 获取该动态的所有父级评论（parent_id = 0）
        $comments = UserDynamicComment::with(['user' => function($query) {
            $query->field('id,nickname,avatar,sex,age,level_id');
        }])
        ->hasWhere('user', function($query) {
            // 确保关联的用户存在且未被删除
            $query->whereNull('delete_time');
        })
        ->where(['parent_id'=>0,'status'=>1,'dynamic_id'=>$this->params['dynamic_id']]) 
        ->order('create_time', 'desc')  // 按时间倒序排列
        ->page($this->pageNo, $this->pageSize)
        ->select();

        // 格式化评论数据
        $list = [];
        foreach ($comments as $comment) {
            // 获取用户等级信息
            $levelInfo = $comment->user->level_info;

            $list[] = [
                'id' => $comment->id,
                'dynamic_id' => $comment->dynamic_id,
                'content' => $comment->content,
                'create_time' => $comment->create_time,
                'user_id' => $comment->user_id,
                'nickname' => $comment->user->nickname,
                'avatar' => $comment->user->avatar,
                'sex' => $comment->user->sex,
                'age' => $comment->user->age,
                'level_bg' => $levelInfo['level_bg'],
            ];
        }

        return $list;
    }
    
    /**
     * @notes 获取评论数量
     * @return int
     */
    public function count(): int
    {
        // 返回该动态的父级评论数量
        return UserDynamicComment::
        hasWhere('user', function($query) {
            // 确保关联的用户存在且未被删除
            $query->whereNull('delete_time');
        })
        ->where(['parent_id'=>0,'status'=>1,'dynamic_id'=>$this->params['dynamic_id']])->count();
    }
}
