<?php

namespace app\adminapi\logic\notice;

use app\common\enum\notice\SmsEnum;
use app\common\logic\BaseLogic;
use app\common\service\ConfigService;

/**
 * 短信配置逻辑层
 * Class SmsConfigLogic
 * @package app\adminapi\logic\notice
 */
class SmsConfigLogic extends BaseLogic
{
    /**
     * @notes 获取短信配置
     * @return array
     */
    public static function getConfig()
    {
        $config = [
            ConfigService::get('sms', 'ali', ['type' => 'ali', 'name' => '阿里云短信', 'status' => 1]),
            ConfigService::get('sms', 'tencent', ['type' => 'tencent', 'name' => '腾讯云短信', 'status' => 0]),
            ConfigService::get('sms', 'smsbao', ['type' => 'smsbao', 'name' => '短信宝短信', 'status' => 1]),
        ];
        return $config;
    }


    /**
     * @notes 短信配置
     * @param $params
     * @return bool|void
     */
    public static function setConfig($params)
    {
        $type = $params['type'];
//        $params['name'] = self::getNameDesc(strtoupper($type));
        $params['name'] = self::getNameDesc($type);
        ConfigService::set('sms', $type, $params);
        $default = ConfigService::get('sms', 'engine', false);
        if ($params['status'] == 1 && $default === false) {
            // 启用当前短信配置 并 设置当前短信配置为默认
//            ConfigService::set('sms', 'engine', strtoupper($type));
            ConfigService::set('sms', 'engine', $type);
            return true;
        }
//        if ($params['status'] == 1 && $default != strtoupper($type)) {
        if ($params['status'] == 1 && $default != $type) {
            // 找到默认短信配置
//            $defaultConfig = ConfigService::get('sms', strtolower($default));
            $defaultConfig = ConfigService::get('sms', $default);
            // 状态置为禁用 并 更新
            $defaultConfig['status'] = 0;
//            ConfigService::set('sms', strtolower($default), $defaultConfig);
            ConfigService::set('sms', $default, $defaultConfig);
            // 设置当前短信配置为默认
//            ConfigService::set('sms', 'engine', strtoupper($type));
            ConfigService::set('sms', 'engine', $type);
            return true;
        }
    }


    /**
     * @notes 查看短信配置详情
     * @param $params
     * @return array|int|mixed|string|null
     */
    public static function detail($params)
    {
        $default = [];
        switch ($params['type']) {
            case 'ali':
                $default = [
                    'sign' => '',
                    'app_key' => '',
                    'secret_key' => '',
                    'status' => 1,
                    'name' => '阿里云短信',
                ];
                break;
            case 'tencent':
                $default = [
                    'sign' => '',
                    'app_id' => '',
                    'secret_key' => '',
                    'status' => 0,
                    'secret_id' => '',
                    'name' => '腾讯云短信',
                ];
                break;
        }
        $result = ConfigService::get('sms', $params['type'], $default);
        $result['status'] = intval($result['status'] ?? 0);
        return $result;
    }


    /**
     * @notes 获取短信平台名称
     * @param $value
     * @return string
     */
    public static function getNameDesc($value)
    {
        $desc = [
            'ali' => '阿里云短信',
            'tencent' => '腾讯云短信',
            'smsbao' => '腾讯云短信',
        ];
        return $desc[$value] ?? '';
    }
}