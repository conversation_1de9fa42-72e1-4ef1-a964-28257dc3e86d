<?php

namespace app\adminapi\validate\dynamic;


use app\common\validate\BaseValidate;


/**
 * UserDynamic验证器
 * Class UserDynamicValidate
 * @package app\adminapi\validate
 */
class DynamicValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',
        'status' => 'require',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        'status' => '状态：0=待审核，1=审核通过，2=审核驳回',

    ];


    /**
     * @notes 添加场景
     * @return UserDynamicValidate
     * <AUTHOR>
     * @date 2025/07/26 15:40
     */
    public function sceneAdd()
    {
        return $this->only(['user_id','content','type','status','like_count','comment_count']);
    }


    /**
     * @notes 编辑场景
     * @return UserDynamicValidate
     * <AUTHOR>
     * @date 2025/07/26 15:40
     */
    public function sceneEdit()
    {
        return $this->only(['id','user_id','content','type','status','like_count','comment_count']);
    }


    /**
     * @notes 删除场景
     * @return UserDynamicValidate
     * <AUTHOR>
     * @date 2025/07/26 15:40
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return UserDynamicValidate
     * <AUTHOR>
     * @date 2025/07/26 15:40
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}