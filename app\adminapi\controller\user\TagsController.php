<?php


namespace app\adminapi\controller\user;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\user\UserImageLabelLists;
use app\adminapi\logic\user\TagsLogic;
use app\adminapi\validate\user\TagsValidate;


/**
 * UserImageLabel控制器
 * Class TagsController
 * @package app\adminapi\controller
 */
class TagsController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function lists()
    {
        $params = $this->request->get();
        $result = TagsLogic::lists($params);
        return $this->success('',$result);
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function add()
    {
        $params = (new TagsValidate())->post()->goCheck('add');
        $result = TagsLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(TagsLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function edit()
    {
        $params = (new TagsValidate())->post()->goCheck('edit');
        $result = TagsLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(TagsLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function delete()
    {
        $params = (new TagsValidate())->post()->goCheck('delete');
        TagsLogic::delete($params);
        return $this->success('删除成功', [], 1, 1);
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function detail()
    {
        $params = (new TagsValidate())->goCheck('detail');
        $result = TagsLogic::detail($params);
        return $this->data($result);
    }

    /**
     * @notes 获取部门数据
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function all()
    {
        $result = TagsLogic::getAllData();
        return $this->data($result);
    }


}