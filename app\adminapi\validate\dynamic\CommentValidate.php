<?php

namespace app\adminapi\validate\dynamic;


use app\common\validate\BaseValidate;


/**
 * UserDynamicComment验证器
 * Class UserDynamicCommentValidate
 * @package app\adminapi\validate
 */
class CommentValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',
        'status' => 'status',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        'status' => '未选择审核状态',

    ];


    /**
     * @notes 添加场景
     * @return UserDynamicCommentValidate
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function sceneAdd()
    {
        return $this->only(['dynamic_id','user_id','parent_id','reply_to_user_id','content']);
    }


    /**
     * @notes 编辑场景
     * @return UserDynamicCommentValidate
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function sceneEdit()
    {
        return $this->only(['id','dynamic_id','user_id','parent_id','reply_to_user_id','content']);
    }


    /**
     * @notes 删除场景
     * @return UserDynamicCommentValidate
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return UserDynamicCommentValidate
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}