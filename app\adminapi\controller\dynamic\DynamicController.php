<?php


namespace app\adminapi\controller\dynamic;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\dynamic\DynamicLists;
use app\adminapi\logic\dynamic\DynamicLogic;
use app\adminapi\validate\dynamic\DynamicValidate;


/**
 * UserDynamic控制器
 * Class UserDynamicController
 * @package app\adminapi\controller
 */
class DynamicController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function lists()
    {
        return $this->dataLists(new DynamicLists());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function audit()
    {
        $params = (new DynamicValidate())->post()->goCheck('edit');
        $result = DynamicLogic::audit($params);
        if (true === $result) {
            return $this->success('审核成功', [], 1, 1);
        }
        return $this->fail(DynamicLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function delete()
    {
        $params = (new DynamicValidate())->post()->goCheck('delete');
        DynamicLogic::delete($params);
        return $this->success('删除成功', [], 1, 1);
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function detail()
    {
        $params = (new DynamicValidate())->goCheck('detail');
        $result = DynamicLogic::detail($params);
        return $this->data($result);
    }


}