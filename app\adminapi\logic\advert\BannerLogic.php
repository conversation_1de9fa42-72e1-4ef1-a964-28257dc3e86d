<?php

namespace app\adminapi\logic\advert;


use app\common\logic\BaseLogic;
use app\common\model\banner\Banner;
use app\common\service\FileService;
use think\facade\Db;

/**
 * Banner逻辑
 * Class BannerLogic
 * @package app\adminapi\logic
 */
class BannerLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @return bool
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            Banner::create([
                'title' => $params['title'],
                'sort' => $params['sort'],
                'position' => $params['position'],
                'desc' => $params['desc'],
                'link' => $params['link'],
                'image' => $params['image'] ? FileService::setFileUrl($params['image']) : '',
                'status' => $params['status'],
                'create_time' => time(),
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            Banner::where('id', $params['id'])->update([
                'title' => $params['title'],
                'sort' => $params['sort'],
                'position' => $params['position'],
                'desc' => $params['desc'],
                'link' => $params['link'],
                'image' => $params['image'] ? FileService::setFileUrl($params['image']) : '',
                'status' => $params['status'],
                'update_time' => time(),
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }



    /**
     * @notes 获取详情
     * @param $params
     * @return array
     */
    public static function detail($params): array
    {
        return Banner::findOrEmpty($params['id'])->toArray();
    }

    /**
     * @notes 修改状态
     * @param $params
     */
    public static function updateStatus($params)
    {
        return Banner::where('id', $params['id'])->update([
            'status' => $params['status'],
            'update_time' => time(),
        ]);
    }
}