<?php
namespace app\adminapi\lists\user;

use app\adminapi\lists\BaseAdminDataLists;
use app\common\enum\user\AccountLogEnum;
use app\common\enum\user\UserTerminalEnum;
use app\common\lists\ListsExcelInterface;
use app\common\model\user\User;
use app\common\model\user\UserInviteRecord;
use think\facade\Db;


/**
 * 用户列表
 * Class UserLists
 * @package app\adminapi\lists\user
 */
class UserLists extends BaseAdminDataLists implements ListsExcelInterface
{

    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
//        return ['keyword','is_online','is_busy','sex','is_auth','is_disable',
//            'is_recommend','is_gaoyan','channel','last_online_time_start',
//            'last_online_time_end','create_time_start','create_time_end'
//        ];
        return [];
    }

    /**
     * @notes 搜索条件
     */
    public function queryWhere()
    {
        $where = [];

        //关键字
        if (!empty($this->params['keyword'])) {
            $where[] = ['u.id|u.nickname|u.mobile', 'like', '%' . $this->params['keyword'] . '%'];
        }
        //在线状态 过滤0值
        if (isset($this->params['is_online']) && $this->params['is_online'] !== '' && $this->params['is_online'] !== null) {
            $where[] = ['u.is_online', '=', $this->params['is_online']];
        }
        //通话状态 过滤0值
        if (isset($this->params['is_busy']) && $this->params['is_busy'] !== '' && $this->params['is_busy'] !== null) {
            $where[] = ['u.is_busy', '=', $this->params['is_busy']];
        }
        //用户性别
        if (!empty($this->params['sex'])) {
            $where[] = ['u.sex', '=', $this->params['sex']];
        }

        //认证状态 过滤0值
        if (isset($this->params['is_auth']) && $this->params['is_busy'] !== '' && $this->params['is_auth'] !== null) {
            $where[] = ['u.is_auth', '=', $this->params['is_auth']];
        }
        //用户状态 过滤0值
        if (isset($this->params['is_disable']) && $this->params['is_disable'] !== '' && $this->params['is_disable'] !== null) {
            $where[] = ['u.is_disable', '=', $this->params['is_disable']];
        }
        //是否推荐 过滤0值
        if (isset($this->params['is_recommend']) && $this->params['is_recommend'] !== '' && $this->params['is_recommend'] !== null) {
            $where[] = ['u.is_recommend', '=', $this->params['is_recommend']];
        }
        //是否高颜 过滤0值
        if (isset($this->params['is_gaoyan']) && $this->params['is_gaoyan'] !== '' && $this->params['is_gaoyan'] !== null) {
            $where[] = ['u.is_gaoyan', '=', $this->params['is_gaoyan']];
        }
        //注册渠道
        if (!empty($this->params['channel'])) {
            $where[] = ['u.channel', '=', $this->params['channel']];
        }
        //最后登陆时间
        if (!empty($this->params['last_online_time_start'])) {
            $where[] = ['u.last_online_time', '>=', strtotime($this->params['last_online_start_time'])];
        }

        if (!empty($this->params['last_online_end_time'])) {
            $where[] = ['u.last_online_time', '<=', strtotime($this->params['last_online_end_time'])];
        }
        //注册时间
        if (!empty($this->params['create_start_time'])) {
            $where[] = ['u.create_time', '>=', strtotime($this->params['create_start_time'])];
        }

        if (!empty($this->params['create_end_time'])) {
            $where[] = ['u.create_time', '<=', strtotime($this->params['create_end_time'])];
        }

        return $where;
    }

    /**
     * @notes 排序规则
     * @return array
     */
    protected function getCommonOrderBy(): array
    {
        return [
            // 1. 首先是否在线倒序
            'is_online' => 'desc',

            // 2. 其次按照是否在线倒序
            'is_busy' => 'desc',

            // 3. 最后按照ID倒序（保证排序稳定性）
            'id' => 'desc'
        ];
    }

    /**
     * @notes 获取用户列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists(): array
    {
        $params = $this->params;

        // 1. 构建子查询：获取每个用户的最新邀请记录ID
        $latestRecordSub = Db::name('user_invite_record')
            ->field('invite_user_id, MAX(id) AS latest_id')
            ->group('invite_user_id')
            ->buildSql(true);

        // 构建主查询
        $query = User::alias('u')
            ->field('u.*, inviter.id as inviter_id, inviter.nickname as inviter_nickname')
            // 直接关联最新记录
            ->leftJoin([$latestRecordSub => 'lr'], 'u.id = lr.invite_user_id')
            // 通过最新记录ID关联原始记录
            ->leftJoin('user_invite_record r', 'lr.latest_id = r.id')
            // 关联邀请人信息
            ->leftJoin('user inviter', 'r.user_id = inviter.id');

        // 处理邀请人搜索条件
        if (isset($params['recommend_id']) && !empty($params['recommend_id'])) {
//            $query->where('r.user_id', $params['recommend_id']);
            // 改为在子查询中过滤，减少JOIN数据量
            $query->whereExists(function ($query) use ($params) {
                $query->name('user_invite_record')
                    ->where('user_id', $params['recommend_id'])
                    ->where('invite_user_id', 'EXP', '= u.id');
            });
        }
        // 其他搜索条件
        $query->where($this->queryWhere(), $this->params);

        $lists = $query->order($this->getCommonOrderBy())
            ->limit($this->limitOffset, $this->limitLength)
            ->select()
            ->append([
                'is_online_text', 'is_busy_text', 'is_auth_text',
                'is_disable_text', 'is_recommend_text', 'is_gaoyan_text', 'channel_text'
            ])
            ->toArray();

        foreach ($lists as &$item) {
            if (!empty($item['inviter_id'])) {
                $item['inviter_info'] = $item['inviter_nickname'] . '(' . $item['inviter_id'] . ')';
            } else {
                $item['inviter_info'] = '平台';
            }
        }

        return $lists;
    }


    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return User::withSearch($this->setSearch(), $this->params)->count();
    }


    /**
     * @notes 导出文件名
     * @return string
     */
    public function setFileName(): string
    {
        return '用户列表';
    }


    /**
     * @notes 导出字段
     * @return string[]
     */
    public function setExcelFields(): array
    {
        return [
            'id' => '用户ID',
            'nickname' => '用户昵称',
            'mobile' => '手机号码',
            'sn' => '用户编号',
            'sex' => '性别',
            'inviter_info' => '推荐人',
            'is_online_text' => '在线状态',
            'is_busy_text' => '通话状态',
            'is_auth_text' => '认证状态',
            'is_disable_text' => '用户状态',
            'is_recommend_text' => '是否推荐',
            'is_gaoyan_text' => '是否高颜',
            'online_time' => '在线时长',
            'voice_price' => '视频价格',
            'video_price' => '语音价格',
            'user_money' => '用户余额',
            'total_recharge_amount' => '累计充值',
            'top_time' => '置顶时间',
            'channel_text' => '注册渠道',
            'login_ip' => '最近登录IP',
            'login_time' => '最近登录时间',
            'register_ip' => '注册IP',
            'create_time' => '注册时间',
        ];
    }

}