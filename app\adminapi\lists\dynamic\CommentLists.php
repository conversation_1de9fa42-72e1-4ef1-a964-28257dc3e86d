<?php

namespace app\adminapi\lists\dynamic;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\user\UserDynamicComment;
use app\common\lists\ListsSearchInterface;


/**
 * UserDynamicComment列表
 * Class UserDynamicCommentLists
 * @package app\adminapi\lists
 */
class CommentLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function setSearch(): array
    {
        return [
            '=' => ['dynamic_id', 'user_id', 'parent_id', 'reply_to_user_id', 'content'],

        ];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function lists(): array
    {
//        return UserDynamicComment::where($this->searchWhere)
//            ->field(['id', 'dynamic_id', 'user_id', 'parent_id', 'reply_to_user_id', 'content'])
//            ->limit($this->limitOffset, $this->limitLength)
//            ->order(['id' => 'desc'])
//            ->select()
//            ->toArray();
        $params = $this->params;
        $query = UserDynamicComment::alias('udc')
            ->join('user u', 'udc.user_id = u.id')
            ->join('user_dynamic ud', 'udc.dynamic_id = ud.id')
            ->whereNull('ud.delete_time')   // 过滤已删除动态
            ->whereNull('u.delete_time')    // 过滤已删除用户
            ->field([
                'udc.id', 'udc.content', 'udc.status', 'udc.create_time',
                'u.id as user_id', 'u.nickname', 'u.mobile',
                'ud.id as dynamic_id', 'ud.content as dynamic_content'
            ]);
        // 添加查询条件
        if (isset($this->params['status']) && $this->params['status'] !== '' && $this->params['status'] !== null) {
            $query->where('udc.status', $params['status']);
        }
        if (!empty($params['keyword'])) {
            $query->Where(function($q) use ($params) {
                $q->whereNull('udc.delete_time')
                    ->where(function($q2) use ($params) {
                        $q2->whereOr('u.id', 'like', '%'.$params['keyword'].'%')
                            ->whereOr('u.nickname', 'like', '%'.$params['keyword'].'%')
                            ->whereOr('u.mobile', 'like', '%'.$params['keyword'].'%');
                    });
            });
        }
        return $query->limit($this->limitOffset, $this->limitLength)
            ->order('udc.id', 'desc')
            ->append(['status_text'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/07/26 17:16
     */
    public function count(): int
    {
        return UserDynamicComment::where($this->searchWhere)->count();
    }

}