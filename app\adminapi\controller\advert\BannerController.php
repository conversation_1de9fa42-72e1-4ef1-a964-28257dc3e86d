<?php


namespace app\adminapi\controller\advert;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\advert\BannerLists;
use app\adminapi\logic\advert\BannerLogic;
use app\adminapi\validate\advert\BannerValidate;


/**
 * Banner控制器
 * Class BannerController
 * @package app\adminapi\controller
 */
class BannerController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     */
    public function lists()
    {
        return $this->dataLists(new BannerLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     */
    public function add()
    {
        $params = (new BannerValidate())->post()->goCheck('add');
        $result = BannerLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(BannerLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     */
    public function edit()
    {
        $params = (new BannerValidate())->post()->goCheck('edit');
        $result = BannerLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(BannerLogic::getError());
    }



    /**
     * @notes 获取详情
     * @return \think\response\Json
     */
    public function detail()
    {
        $params = (new BannerValidate())->post()->goCheck('detail');
        $result = BannerLogic::detail($params);
        return $this->data($result);
    }

    /**
     * @notes 修改轮播图状态
     * @return \think\response\Json
     */
    public function status()
    {
        $params = (new BannerValidate())->post()->goCheck('status');
        BannerLogic::updateStatus($params);
        return $this->success('修改成功', [], 1, 1);
    }
}