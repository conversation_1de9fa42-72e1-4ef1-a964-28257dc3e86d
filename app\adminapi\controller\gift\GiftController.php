<?php


namespace app\adminapi\controller\gift;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\gift\GiftLists;
use app\adminapi\logic\gift\GiftLogic;
use app\adminapi\validate\gift\GiftValidate;


/**
 * Gift控制器
 * Class GiftController
 * @package app\adminapi\controller
 */
class GiftController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function lists()
    {
        return $this->dataLists(new GiftLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function add()
    {
        $params = (new GiftValidate())->post()->goCheck('add');
        $result = GiftLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(GiftLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function edit()
    {
        $params = (new GiftValidate())->post()->goCheck('edit');
        $result = GiftLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(GiftLogic::getError());
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function detail()
    {
        $params = (new GiftValidate())->goCheck('detail');
        $result = GiftLogic::detail($params);
        return $this->data($result);
    }


}