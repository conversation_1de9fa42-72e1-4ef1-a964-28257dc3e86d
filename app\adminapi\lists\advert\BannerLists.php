<?php

namespace app\adminapi\lists\advert;



use app\adminapi\lists\BaseAdminDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\banner\Banner;


/**
 * Banner列表
 * Class BannerLists
 * @package app\adminapi\lists
 */
class BannerLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     */
    public function setSearch(): array
    {
        $searchFields = ['title', 'status'];
        // 获取两个数组交集
        return array_intersect(array_keys($this->params), $searchFields);
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists(): array
    {
        return Banner::withSearch($this->setSearch(), $this->params)
            ->field(['id', 'title', 'sort', 'position', 'desc', 'link', 'image', 'status','create_time'])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['sort'=>'desc','id'=>'desc'])
            ->append(['position_desc'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     */
    public function count(): int
    {
        return Banner::where($this->searchWhere)->count();
    }

}