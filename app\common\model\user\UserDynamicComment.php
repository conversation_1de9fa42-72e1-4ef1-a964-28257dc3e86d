<?php

namespace app\common\model\user;

use app\common\model\BaseModel;
use think\model\concern\SoftDelete;

/**
 * 用户动态评论模型
 * Class UserDynamicComment
 * @package app\common\model\user
 */
class UserDynamicComment extends BaseModel
{
    use SoftDelete;

    protected $name = 'user_dynamic_comment';
    protected $deleteTime = 'delete_time';

    /**
     * @notes 关联用户模型（评论者）
     * @return \think\model\relation\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * @notes 关联动态模型
     * @return \think\model\relation\BelongsTo
     */
    public function dynamic()
    {
        return $this->belongsTo(UserDynamic::class, 'dynamic_id', 'id');
    }

    /**
     * @notes 关联父评论模型
     * @return \think\model\relation\BelongsTo
     */
    public function parentComment()
    {
        return $this->belongsTo(UserDynamicComment::class, 'parent_id', 'id');
    }

    /**
     * @notes 关联子评论模型（回复）
     * @return \think\model\relation\HasMany
     */
    public function replies()
    {
        return $this->hasMany(UserDynamicComment::class, 'parent_id', 'id');
    }

    /**
     * @notes 关联被回复用户模型
     * @return \think\model\relation\BelongsTo
     */
    public function replyToUser()
    {
        return $this->belongsTo(User::class, 'reply_to_user_id', 'id');
    }


    /**
     * @notes 搜索器-用户ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchUserIdAttr($query, $value, $data)
    {
        if ($value) {
            $query->where('user_id', $value);
        }
    }

    /**
     * @notes 搜索器-父评论ID
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchParentIdAttr($query, $value, $data)
    {
        if ($value !== null) {
            $query->where('parent_id', $value);
        }
    }

    /**
     * @notes 状态文本获取器
     * @param $value
     * @param $data
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? $this->status;
        switch ($status) {
            case 0:
                return '审核中';
            case 1:
                return '已发布';
            case 2:
                return '审核未通过';
            default:
                return '未知状态';
        }
    }

}
