<?php

namespace app\adminapi\validate\gift;


use app\common\validate\BaseValidate;


/**
 * Gift验证器
 * Class GiftValidate
 * @package app\adminapi\validate
 */
class GiftValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',
        'type' => 'require',
        'gift_type' => 'require',
        'is_all_notify' => 'require',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        'type' => '礼物类型1普遍2守护3动画svge',
        'gift_type' => '1普通 2连续',
        'is_all_notify' => '是否全局推送',

    ];


    /**
     * @notes 添加场景
     * @return GiftValidate
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function sceneAdd()
    {
        return $this->only(['type','gift_type','is_all_notify']);
    }


    /**
     * @notes 编辑场景
     * @return GiftValidate
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function sceneEdit()
    {
        return $this->only(['id','type','gift_type','is_all_notify']);
    }


    /**
     * @notes 删除场景
     * @return GiftValidate
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return GiftValidate
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}