<?php

namespace app\adminapi\logic\audit;


use app\common\logic\BaseLogic;
use app\common\model\user\User;
use app\common\model\user\UserDataReview;
use app\common\model\user\UserPhotoWall;
use think\facade\Db;


/**
 * UserDataReview逻辑
 * Class AuditLogic
 * @package app\adminapi\logic
 */
class AuditLogic extends BaseLogic
{

    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/22 20:05
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            $userDataReview = UserDataReview::find($params['id']);
            $userDataReview->status = $params['status'];
            $userDataReview->reason = $params['reason'];
            $userDataReview->save();

            switch ($params['type']) {
                // 审核头像  更新user表avatar、thumb_avatar字段
                case '1':
                    $userInfo = User::find($params['user_id']);
                    $userInfo->avatar = $userDataReview->image;
                    $userInfo->thumb_avatar = $userDataReview->thumb_image;
                    $userInfo->save();
                    break;

                // 审核照片墙  查询user_photo_wall表根据user_id先删除，再插入
                case '2':
                    UserPhotoWall::where('user_id', $params['user_id'])->delete();
                    foreach (json_decode($userDataReview->image) as $image) {
                        UserPhotoWall::create([
                            'user_id' => $params['user_id'],
                            'type' => 1,
                            'url' => $image,
                        ]);
                    }
                    break;

                // 审核主播认证照片  更新user表is_auth字段为1
                case '3':
                    $userInfo = User::find($params['user_id']);
                    $userInfo->is_auth = 1;
                    $userInfo->save();
                    break;
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
}