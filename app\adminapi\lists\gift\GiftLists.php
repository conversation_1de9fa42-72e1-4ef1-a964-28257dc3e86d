<?php

namespace app\adminapi\lists\gift;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\gift\Gift;
use app\common\lists\ListsSearchInterface;


/**
 * Gift列表
 * Class GiftLists
 * @package app\adminapi\lists
 */
class GiftLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function setSearch(): array
    {
        return [
            '=' => ['name', 'coin', 'img', 'svga', 'addtime', 'orderno', 'type', 'gift_type', 'is_all_notify', 'is_accost'],

        ];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function lists(): array
    {
        return Gift::where($this->searchWhere)
            ->field(['id', 'name', 'coin', 'img', 'svga', 'addtime', 'orderno', 'type', 'gift_type', 'is_all_notify', 'is_accost'])
            ->append(['type_desc','gift_type_desc','is_all_notify_desc','is_accost_desc'])
            ->limit($this->limitOffset, $this->limitLength)
            ->order(['id' => 'desc'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/07/26 09:09
     */
    public function count(): int
    {
        return Gift::where($this->searchWhere)->count();
    }

}