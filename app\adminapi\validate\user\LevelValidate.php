<?php

namespace app\adminapi\validate\user;


use app\common\validate\BaseValidate;


/**
 * Level验证器
 * Class LevelValidate
 * @package app\adminapi\validate
 */
class LevelValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'levelid' => 'require',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'levelid' => 'levelid',

    ];


    /**
     * @notes 添加场景
     * @return LevelValidate
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function sceneAdd()
    {
        return $this->remove('levelid', true);
    }


    /**
     * @notes 编辑场景
     * @return LevelValidate
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function sceneEdit()
    {
        return $this->only(['levelid']);
    }


    /**
     * @notes 删除场景
     * @return LevelValidate
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function sceneDelete()
    {
        return $this->only(['levelid']);
    }


    /**
     * @notes 详情场景
     * @return LevelValidate
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function sceneDetail()
    {
        return $this->only(['levelid']);
    }

}