<?php

namespace app\adminapi\validate\user;


use app\common\validate\BaseValidate;


/**
 * UserImageLabel验证器
 * Class TagsValidate
 * @package app\adminapi\validate
 */
class TagsValidate extends BaseValidate
{

    /**
     * 设置校验规则
     * @var string[]
     */
    protected $rule = [
        'id' => 'require',
        'name' => 'require',
        'sort' => 'require',

    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        'name' => '形象标签名称',
        'sort' => '排序',

    ];


    /**
     * @notes 添加场景
     * @return TagsValidate
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function sceneAdd()
    {
        return $this->only(['name','sort']);
    }


    /**
     * @notes 编辑场景
     * @return TagsValidate
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function sceneEdit()
    {
        return $this->only(['id','name','sort']);
    }


    /**
     * @notes 删除场景
     * @return TagsValidate
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return TagsValidate
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}