<?php

namespace app\common\model;

use app\common\service\FileService;
use think\Model;

/**
 * 基础模型
 * Class BaseModel
 * @package app\common\model
 */
class BaseModel extends Model
{
    /**
     * @notes 公共处理图片,补全路径
     * @param $value
     * @return string
     */
    public function getImageAttr($value)
    {
        $images = json_decode($value, true);
        if (is_array($images)) {
            foreach ($images as &$v) {
                $v = FileService::getFileUrl(trim($v));
            }
            return $images;
        }
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
//        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }

    /**
     * @notes 公共图片处理,去除图片域名
     * @param $value
     * @return mixed|string
     */
    public function setImageAttr($value)
    {
        return (!empty($value) && is_string($value)) ? FileService::setFileUrl(trim($value)) : '';
    }
}