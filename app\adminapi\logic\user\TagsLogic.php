<?php

namespace app\adminapi\logic\user;


use app\common\model\user\UserImageLabel;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * UserImageLabel逻辑
 * Class TagsLogic
 * @package app\adminapi\logic
 */
class TagsLogic extends BaseLogic
{


    /**
     * @notes 形象标签列表
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function lists($params)
    {
        $where = [];
        if (!empty($params['name'])) {
            $where[] = ['name', 'like', '%' . $params['name'] . '%'];
        }
        $lists = UserImageLabel::where($where)
            ->order(['sort' => 'desc', 'id' => 'desc'])
            ->select()
            ->toArray();

        $pid = 0;
        if (!empty($lists)) {
            $pid = min(array_column($lists, 'pid'));
        }
        return self::getTree($lists, $pid);
    }

    /**
     * @notes 添加
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public static function add(array $params): bool
    {
        Db::startTrans();
        try {
            UserImageLabel::create([
                'pid' => $params['pid'],
                'inco' => $params['inco'],
                'name' => $params['name'],
                'sort' => $params['sort'],
                'color' => $params['color'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public static function edit(array $params): bool
    {
        Db::startTrans();
        try {
            UserImageLabel::where('id', $params['id'])->update([
                'pid' => $params['pid'],
                'inco' => $params['inco'],
                'name' => $params['name'],
                'sort' => $params['sort'],
                'color' => $params['color'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public static function delete(array $params): bool
    {
        return UserImageLabel::destroy($params['id']);
    }


    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/07/28 18:11
     */
    public static function detail($params): array
    {
        return UserImageLabel::findOrEmpty($params['id'])->toArray();
    }

    /**
     * @notes 部门数据
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getAllData()
    {
        $data = UserImageLabel::order(['sort' => 'desc', 'id' => 'desc'])
            ->select()
            ->toArray();

        $pid = min(array_column($data, 'pid'));
        return self::getTree($data, $pid);
    }

    /**
     * @notes 列表树状结构
     * @param $array
     * @param int $pid
     * @param int $level
     * @return array
     */
    public static function getTree($array, $pid = 0, $level = 0)
    {
        $list = [];
        foreach ($array as $key => $item) {
            if ($item['pid'] == $pid) {
                $item['level'] = $level;
                $item['children'] = self::getTree($array, $item['id'], $level + 1);
                $list[] = $item;
            }
        }
        return $list;
    }
}