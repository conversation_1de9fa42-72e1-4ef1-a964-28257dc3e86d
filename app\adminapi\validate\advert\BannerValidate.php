<?php

namespace app\adminapi\validate\advert;


use app\common\validate\BaseValidate;

/**
 * Banner验证器
 * Class BannerValidate
 * @package app\adminapi\validate
 */
class BannerValidate extends BaseValidate
{

     /**
      * 设置校验规则
      * @var string[]
      */
     protected $rule = [
        'id'     => 'require',
        'title'  => 'require',
        'sort'   => 'require',
        'position' => 'require|in:1',
        'image'  => 'require',
        'status' => 'require|in:0,1'
    ];


    protected $message = [
        'id.require' => 'ID不能为空',
        'title.require' => '标题不能为空',
        'sort.require' => '排序不能为空',
        'position.in' => '位置参数错误',
        'image.require' => '请上传图片',
        'status.in' => '状态参数错误'
    ];


    /**
     * @notes 添加场景
     * @return BannerValidate
     */
    public function sceneAdd()
    {
        return $this->only(['title','sort','position','link','image','status']);
    }


    /**
     * @notes 编辑场景
     * @return BannerValidate
     */
    public function sceneEdit()
    {
        return $this->only(['id','title','sort','position','link','image','status']);
    }


    /**
     * @notes 删除场景
     * @return BannerValidate
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return BannerValidate
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

    /**
     * @notes 状态场景
     * @return BannerValidate
     */
    public function sceneStatus()
    {
        return $this->only(['id','status']);
    }
}