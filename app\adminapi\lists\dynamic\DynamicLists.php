<?php

namespace app\adminapi\lists\dynamic;


use app\adminapi\lists\BaseAdminDataLists;
use app\common\model\user\UserDynamic;
use app\common\lists\ListsSearchInterface;


/**
 * UserDynamic列表
 * Class UserDynamicLists
 * @package app\adminapi\lists
 */
class DynamicLists extends BaseAdminDataLists implements ListsSearchInterface
{


    /**
     * @notes 设置搜索条件
     * @return \string[][]
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function setSearch(): array
    {
        return [
            '=' => [ 'type', 'status'],
        ];
    }


    /**
     * @notes 获取列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function lists(): array
    {

        $params = $this->params;
        $query = UserDynamic::alias('ud')
            ->join('user u', 'ud.user_id = u.id')
            ->whereNull('ud.delete_time')
            ->whereNull('u.delete_time')
            ->field('ud.id, ud.user_id, ud.content, ud.type, ud.media_urls, ud.status, ud.privacy_type, ud.like_count,ud.comment_count,ud.create_time, u.nickname, u.mobile');

        // 添加查询条件
        if (isset($this->params['status']) && $this->params['status'] !== '' && $this->params['status'] !== null) {
            $query->where('udr.status', $params['status']);
        }
        if (isset($this->params['type']) && $this->params['type'] !== '' && $this->params['type'] !== null) {
            $query->where('ud.type', $params['type']);
        }
        if (!empty($params['keyword'])) {
            $query->Where(function($q) use ($params) {
                $q->whereNull('ud.delete_time')
                    ->where(function($q2) use ($params) {
                        $q2->whereOr('u.id', 'like', '%'.$params['keyword'].'%')
                            ->whereOr('u.nickname', 'like', '%'.$params['keyword'].'%')
                            ->whereOr('u.mobile', 'like', '%'.$params['keyword'].'%');
                    });
            });
        }
        return $query->limit($this->limitOffset, $this->limitLength)
            ->order('ud.id', 'desc')
            ->append(['status_text','privacy_type_text','type_text'])
            ->select()
            ->toArray();
    }


    /**
     * @notes 获取数量
     * @return int
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public function count(): int
    {
        return UserDynamic::where($this->searchWhere)->count();
    }

}