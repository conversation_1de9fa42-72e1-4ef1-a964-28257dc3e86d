<?php


namespace app\adminapi\controller\user;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\user\LevelLists;
use app\adminapi\logic\user\LevelLogic;
use app\adminapi\validate\user\LevelValidate;


/**
 * Level控制器
 * Class LevelController
 * @package app\adminapi\controller
 */
class LevelController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function lists()
    {
        return $this->dataLists(new LevelLists());
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function add()
    {
        $params = (new LevelValidate())->post()->goCheck('add');
        $result = LevelLogic::add($params);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(LevelLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function edit()
    {
        $params = (new LevelValidate())->post()->goCheck('edit');
        $result = LevelLogic::edit($params);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(LevelLogic::getError());
    }



    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/07/28 16:29
     */
    public function detail()
    {
        $params = (new LevelValidate())->goCheck('detail');
        $result = LevelLogic::detail($params);
        return $this->data($result);
    }


}