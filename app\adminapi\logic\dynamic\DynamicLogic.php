<?php

namespace app\adminapi\logic\dynamic;


use app\common\model\user\UserDynamic;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * UserDynamic逻辑
 * Class UserDynamicLogic
 * @package app\adminapi\logic
 */
class DynamicLogic extends BaseLogic
{
    /**
     * @notes 审核
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public static function audit(array $params): bool
    {
        Db::startTrans();
        try {
            $userDynamic = UserDynamic::find($params['id']);
            $userDynamic->status = $params['status'];
            $userDynamic->save();
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public static function delete(array $params): bool
    {
        Db::startTrans();
        try {
            $userDynamic = UserDynamic::find($params['id']);
            $userDynamic->delete_time = time();
            $userDynamic->save();
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 获取详情
     * @param $params
     * @return array
     * <AUTHOR>
     * @date 2025/07/26 15:22
     */
    public static function detail($params): array
    {
        return UserDynamic::findOrEmpty($params['id'])
            ->append(['status_text','privacy_type_text','type_text'])
            ->toArray();
    }
}